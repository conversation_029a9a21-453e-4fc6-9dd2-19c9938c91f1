import request from "@/utils/request";
import { type UserResponseData } from './type'

enum API {
  USER_LIST = "/admin/acl/user/",
  //添加一个新的用户账号
  ADDUSER_URL = '/admin/acl/user/save',
  //更新已有的用户账号
  UPDATEUSER_URL = '/admin/acl/user/update',
  //删除某一个账号
  DELETEUSER_URL = '/admin/acl/user/remove/',
  //获取全部职位,当前账号拥有的职位接口
  ALLROLEURL = '/admin/acl/user/toAssign/',
  //给已有的用户分配角色接口
  SETROLE_URL = '/admin/acl/user/doAssignRole',
}
// 获取用户列表
export const getUserList = (page: number, size: number,username: string) => request.get<any, UserResponseData>(API.USER_LIST + `${page}/${size}/?username=${username}`)
//添加用户与更新已有用户的接口
export const reqAddOrUpdateUser = (data: any) => {
  //携带参数有ID更新
  if (data.id) {
    return request.put<any, any>(API.UPDATEUSER_URL, data)
  } else {
    return request.post<any, any>(API.ADDUSER_URL, data)
  }
}
//删除某一个账号的信息
export const reqRemoveUser = (userId: number) =>
  request.delete<any, any>(API.DELETEUSER_URL + userId)

//获取全部职位以及包含当前用户的已有的职位
export const reqAllRole = (userId: number) =>
  request.get<any, any>(API.ALLROLEURL + userId)
//分配职位
export const reqSetUserRole = (data: any) =>
  request.post<any, any>(API.SETROLE_URL, data)
