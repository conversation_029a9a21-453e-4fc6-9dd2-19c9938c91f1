// 路由鉴权
import { nextTick } from 'vue'
import router from './router'
import pinia from './store'
import nprogress from 'nprogress'
import 'nprogress/nprogress.css'
import setting from './setting'
import { constantRoutes, asyncRoutes, anyRouter } from '@/router/routers'
nprogress.configure({
  showSpinner: false,
})
import { getToken } from '@/utils/storage'
import useUserStore from '@/store/model/user'
// 过滤异步路由
const filterAsyncRoute = (asyncRoutes: any, routes: any) => {
  return asyncRoutes.filter((route: any) => {
    if (routes.indexOf(route.name) !== -1) {
      if (route.children) {
        route.children = filterAsyncRoute(route.children, routes)
      }
      return true
    }
    return false
  })

}

router.beforeEach(async (to, from, next) => {
  nprogress.start()
  document.title = `${setting.title}-${to.meta.title}`
  let userStore = useUserStore()
  let token = userStore.token
  let username = userStore.username

  if (token) {
    if (to.path == '/login') {
      next('/')
    } else {
      if (username) {
        next()
      } else {
        try {
          await userStore.getUserInfo();

          //放行
          //万一:刷新的时候是异步路由,有可能获取到用户信息、异步路由还没有加载完毕,出现空白的效果
          next({ ...to, replace: true })
        } catch (error) {
          userStore.logout()
          next({ path: '/login', query: { redirect: to.path, date: new Date().getTime() } })
        }
      }
    }
  } else {
    if (to.path == '/login') {
      next()
    } else {
      // 
      next({ path: '/login', query: { redirect: to.path, date: new Date().getTime() } })
    }
  }

})

router.afterEach((to, from) => {
  nprogress.done()
})