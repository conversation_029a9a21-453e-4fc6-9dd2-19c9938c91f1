<template>
	<div>
		<el-form
			ref="ruleFormRef"
			:model="ruleForm"
			:rules="rules"
			label-width="auto"
			class="demo-ruleForm"
			status-icon
		>
			<el-form-item label="用户名" prop="username">
				<el-input v-model="ruleForm.username" />
			</el-form-item>
			<el-form-item label="密 码" prop="password">
				<el-input v-model="ruleForm.password" type="password" show-password />
			</el-form-item>

			<el-form-item>
				<el-button style="width: 100%" type="primary" @click="submitForm">
					提交
				</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElNotification, ElMessage } from 'element-plus';
import { validateName } from '@/utils/validator';
import useUserStore from '@/store/model/user';
let router = useRouter();
let route = useRoute();
let userserStore = useUserStore();
let ruleFormRef = ref();
interface RuleForm {
	username: string;
	password: string;
}
let ruleForm = reactive<RuleForm>({
	username: 'admin',
	password: '111111',
});
const rules = reactive<FormRules<typeof ruleForm>>({
	username: [{ required: true, validator: validateName, trigger: 'blur' }],
	password: [
		{ required: true, message: '请输入密码', trigger: 'blur' },
		{ min: 6, max: 15, message: '密码长度在 6 到 15 个字符', trigger: 'blur' },
	],
});
watch(route, (to, from) => {
	console.log('tos刷新', to);
	router.go(0);
});
const submitForm = async () => {
	await ruleFormRef.value.validate();
	try {
		await userserStore.userLogin(ruleForm);
		let redirect = route.query.redirect as string;
		router.push({ path: redirect || '/' });
		ElNotification({
			title: '登录成功',
			message: `Hi,欢迎回来`,
			type: 'success',
			duration: 2000,
		});
	} catch (error) {
		console.log(error);
	}
};
</script>

<style scoped lang="scss"></style>
