<template>
	<div class="main-height">

	<router-view v-slot="{ Component }">
		<transition name="fade">
			<component :is="Component" />
		</transition>
	</router-view>
	
</div>
</template>

<script setup lang="ts">
import { watch, ref, nextTick } from 'vue';
import useSettingStore from '@/store/model/setting';
let settingStore = useSettingStore();
let flag = ref(true);
watch(
	() => settingStore.relsh,
	(newVal) => {
		flag.value = false;
		nextTick(() => {
			flag.value = true;
		});
	}
);
</script>

<style scoped lang="scss">
.main-height{
	height: calc(100vh - $base-tabbar-height - $base-tag-height);
	padding: 10px 20px;
}
.fade-enter-from {
	opacity: 0;
	transition: opacity 1s ease-in-out;
}

.fade-enter-active {
	transition: all 0.3s;
}

.fade-enter-to {
	opacity: 1;
}
</style>
