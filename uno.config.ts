import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
} from "unocss";

export default defineConfig({
  // 预设
  presets: [
    // 默认预设
    presetUno(),
    // 属性化模式支持
    presetAttributify(),
    // 图标预设
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  // 自定义规则
  rules: [
    // 可以在这里添加自定义的CSS规则
  ],
  // 自定义快捷方式
  shortcuts: {
    // 可以在这里添加自定义的快捷方式
    btn: "py-2 px-4 font-semibold rounded-lg shadow-md",
    "btn-primary": "btn text-white bg-blue-500 hover:bg-blue-700",
    "btn-secondary": "btn text-gray-700 bg-gray-200 hover:bg-gray-300",
  },
  // 主题配置
  theme: {
    colors: {
      // 可以在这里自定义颜色
    },
  },
});
