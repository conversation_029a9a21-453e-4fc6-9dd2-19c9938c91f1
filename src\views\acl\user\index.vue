<template>
  <div>
    <el-card style="height: 80px">
      <el-form :inline="true" class="form">
        <el-form-item label="用户名:">
          <el-input
            placeholder="请你输入搜索用户名"
            v-model="keyword"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="default"
            :disabled="keyword ? false : true"
            @click="search"
          >
            搜索
          </el-button>
          <el-button type="primary" size="default" @click="reset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-card>
    <el-card style="margin: 10px 0px">
      <el-button type="primary" size="default" @click="addUser">
        添加用户
      </el-button>
      <el-button
        type="primary"
        size="default"
        :disabled="selectIdArr.length ? false : true"
        @click="deleteSelectUser"
      >
        批量删除
      </el-button>
      <!-- table展示用户信息 -->
      <el-table
        @selection-change="selectChange"
        style="margin: 10px 0px"
        border
        :data="userArr"
      >
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column
          label="#"
          align="center"
          type="index"
        ></el-table-column>
        <el-table-column label="ID" align="center" prop="id"></el-table-column>
        <el-table-column
          label="用户名字"
          align="center"
          prop="username"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="用户名称"
          align="center"
          prop="name"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="用户角色"
          align="center"
          prop="roleName"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="更新时间"
          align="center"
          prop="updateTime"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="操作" width="300px" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              icon="User"
              @click="setRole(row)"
            >
              分配角色
            </el-button>
            <el-button
              type="primary"
              size="small"
              icon="Edit"
              @click="updateUser(row)"
            >
              编辑
            </el-button>
            <el-popconfirm
              :title="`你确定要删除${row.username}?`"
              width="260px"
              @confirm="deleteUser(row.id)"
            >
              <template #reference>
                <el-button type="primary" size="small" icon="Delete">
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[5, 7, 9, 11]"
        size="small"
        :background="true"
        layout="prev, pager, next, jumper,->,sizes,total"
        :total="total"
        @current-change="getHasUser"
        @size-change="handler"
      />
    </el-card>
    <userDiog
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @closeDiog="closeDiog"
      :updateId="updateId"
      :updateUserForm="updateUserForm"
      @getList="getList"
    ></userDiog>
    <role
      v-if="dialogVisibleRole"
      :dialogVisibleRole="dialogVisibleRole"
      :roleList="roleList"
      @closeRole="closeRole"
      @getList="getList"
    ></role>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { getUserList, reqRemoveUser } from "@/api/acl/user";
import { ElMessage } from "element-plus";
import userDiog from "./child/userDiog.vue";
import role from "./child/role.vue";
let pageNo = ref<number>(1);
let pageSize = ref<number>(5);
let total = ref<number>(100);
let dialogVisible = ref<boolean>(false);
let updateId = ref<number>();
let updateUserForm = reactive({});
let dialogVisibleRole = ref<boolean>(false);
let roleList = ref<object>({});
let setRole = (row: any) => {
  roleList.value = row;
  dialogVisibleRole.value = true;
};
let keyword = ref<string>("");
let selectIdArr = ref<[]>([]);
let userArr = ref<any>([]);

onMounted(() => {
  getList();
});

let getList = async () => {
  let res = await getUserList(pageNo.value, pageSize.value, keyword.value);
  if (res.code == 200) {
    userArr.value = res.data.records;
    total.value = res.data.total;
  }
};
let search = () => {
  getList();
};
let reset = () => {
  pageNo.value = 1;
  pageSize.value = 5;
  keyword.value = "";
  getList();
};
let removeUser = async (id: number) => {
  getList();
};

let addUser = () => {
  dialogVisible.value = true;
};
let closeDiog = () => {
  updateId.value = 0;
  dialogVisible.value = false;
};
let deleteSelectUser = () => {};
let closeRole = () => {
  dialogVisibleRole.value = false;
};
let selectChange = (val: any) => {};
let updateUser = (row: any) => {
  updateId.value = row.id;
  Object.assign(updateUserForm, row);
  dialogVisible.value = true;
};
let deleteUser = async (id: number) => {
  let res = await reqRemoveUser(id);
  if (res.code == 200) {
    ElMessage.success("删除成功");
    getList();
  } else {
    ElMessage.error(res.message);
  }
};
let getHasUser = (page: number) => {
  pageNo.value = page;
  getList();
};
let handler = (size: number) => {
  pageSize.value = size;
  getList();
};
</script>

<style lang="scss" scoped></style>
