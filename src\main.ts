import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import pinia from "./store";

// element-plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
// SVG图标注册
import "virtual:svg-icons-register";

// 全局组件注册
import gloablComponent from "./components/index";

// 全局样式
import "@/styles/index.scss";
// UnoCSS样式
import "uno.css";

// 路由鉴权
import "./permission";

// 创建应用
const app = createApp(App);
app.config.globalProperties.x = 99;
app.use(router);
app.use(pinia);
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(gloablComponent);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 挂载应用
app.mount("#app");
