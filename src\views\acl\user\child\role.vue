<template>
	<el-dialog
		:modelValue="dialogVisibleRole"
		:title="`分配角色-${roleList.name}`"
		width="500"
		:before-close="handleClose"
	>
		<el-checkbox
			v-model="checkAll"
			:indeterminate="isIndeterminate"
			@change="handleCheckAllChange"
		>
			全选
		</el-checkbox>
		<el-checkbox-group
			v-model="checkedCities"
			@change="handleCheckedCitiesChange"
		>
			<el-checkbox
				v-for="item in allRolesList"
				:key="item.id"
				:label="item.roleName"
				:value="item.id"
			>
				{{ item.roleName }}
			</el-checkbox>
		</el-checkbox-group>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="submit">确认</el-button>
				<el-button type="primary" @click="handleClose">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { reqAllRole, reqSetUserRole } from '@/api/acl/user';
import { ElMessage } from 'element-plus';
let emit = defineProps({
	dialogVisibleRole: {
		type: Boolean,
		default: false,
	},
	roleList: {
		type: Object,
		default: {},
	},
});
let emits = defineEmits(['closeRole', 'getList']);
import { ref } from 'vue';
const checkAll = ref(false);
const isIndeterminate = ref(true);
const checkedCities = ref([]);
let allRolesList: any = ref([]);
onMounted(() => {
	getRoleList();
});
const getRoleList = async () => {
	let res = await reqAllRole(emit.roleList.id);
	if (res.code == 200) {
		allRolesList.value = res.data.allRolesList;
    checkedCities.value = res.data.assignRoles.map((item: any) => item.id);
    
	}
};
const handleCheckAllChange = (val: boolean) => {
	checkedCities.value = val ? allRolesList.value : [];
	isIndeterminate.value = false;
};
const handleCheckedCitiesChange = (value: string[]) => {
	const checkedCount = value.length;
	checkAll.value = checkedCount === allRolesList.value.length;
	isIndeterminate.value =
		checkedCount > 0 && checkedCount < allRolesList.value.length;
};
const submit = async () => {
	let data = {
		userId: emit.roleList.id,
		roleIdList: checkedCities.value,
	};
	let res = await reqSetUserRole(data);
	if (res.code == 200) {
		emits('closeRole');
		emits('getList');
		ElMessage.success('分配成功');
	} else {
		ElMessage.error('分配失败');
	}
};
const handleClose = () => {
	emits('closeRole');
};
</script>

<style lang="scss" scoped></style>
