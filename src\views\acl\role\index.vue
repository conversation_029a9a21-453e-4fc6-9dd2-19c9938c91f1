<template>
  <div>
    <el-card style="height: 80px">
      <el-form :inline="true" class="form">
        <el-form-item label="角色名:">
          <el-input
            placeholder="请你输入搜索用户名"
            v-model="keyword"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="default" @click="search">
            搜索
          </el-button>
          <el-button type="primary" size="default" @click="reset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card style="margin: 10px 0px">
      <el-button type="primary" size="default" @click="addRole">
        添加角色
      </el-button>
      <!-- table展示用户信息 -->
      <el-table style="margin: 10px 0px" border :data="roleArr">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column
          label="#"
          align="center"
          type="index"
        ></el-table-column>
        <el-table-column label="ID" align="center" prop="id"></el-table-column>
        <el-table-column
          label="角色名车给"
          align="center"
          prop="roleName"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="更新时间"
          align="center"
          prop="updateTime"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="操作" width="300px" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              icon="User"
              @click="setRole(row)"
            >
              分配权限
            </el-button>
            <el-button
              type="primary"
              size="small"
              icon="Edit"
              @click="updateUser(row)"
            >
              编辑
            </el-button>
            <el-popconfirm
              :title="`你确定要删除${row.roleName}?`"
              width="260px"
              @confirm="deleteUser(row.id)"
            >
              <template #reference>
                <el-button type="primary" size="small" icon="Delete">
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="pageNo"
        v-model:page-size="pageSize"
        size="small"
        :page-sizes="[5, 7, 9, 11]"
        :background="true"
        layout="prev, pager, next, jumper,->,sizes,total"
        :total="total"
        @current-change="getHasUser"
        @size-change="handler"
      />
    </el-card>
    <roleDiog
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @closeDiog="closeDiog"
      :itemObj="itemObj"
      @getList="getList"
    ></roleDiog>
    <roleAuth
      v-if="roleAuthDiog"
      :roleAuthDiog="roleAuthDiog"
      @closeRoleDiog="closeRoleDiog"
      :roleId="roleId"
      @getList="getList"
    ></roleAuth>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { reqAllRoleList, reqRemoveRole } from "@/api/acl/role";
import roleDiog from "./child/roleDiog.vue";
import roleAuth from "./child/roleAuth.vue";
let keyword = ref<string>("");
let pageNo = ref<number>(1);
let pageSize = ref<number>(5);
let total = ref<number>(0);
let roleArr = ref<[]>([]);
let dialogVisible = ref<boolean>(false);
let itemObj = ref<any>({});
let roleId = ref<number>(0);
let roleAuthDiog = ref<boolean>(false);

onMounted(() => {
  getList();
});
let getList = async () => {
  let res = await reqAllRoleList(pageNo.value, pageSize.value, keyword.value);
  if (res.code == 200) {
    roleArr.value = res.data.records;
    total.value = res.data.total;
  }
};

let search = () => {
  getList();
};
let reset = () => {
  pageNo.value = 1;
  pageSize.value = 5;
  keyword.value = "";
  getList();
};
let addRole = () => {
  itemObj.value = {};
  dialogVisible.value = true;
};
let closeDiog = () => {
  dialogVisible.value = false;
};
let setRole = (row: any) => {
  roleAuthDiog.value = true;
  roleId.value = row.id;
};
let closeRoleDiog = () => {
  roleAuthDiog.value = false;
};
let updateUser = (row: any) => {
  itemObj.value = row;
  dialogVisible.value = true;
};
let deleteUser = async (id: number) => {
  let res = await reqRemoveRole(id);
  if (res.code == 200) {
    ElMessage.success(res.message);
    getList();
  } else {
    ElMessage.error(res.message);
  }
};
let getHasUser = (val: number) => {
  pageNo.value = val;
  getList();
};
let handler = (val: number) => {
  pageSize.value = val;
  getList();
};
</script>
