<template>
	<el-dialog
		:modelValue="roleAuthDiog"
		title="分配权限"
		width="500"
		:before-close="handleClose"
	>
		<el-tree
			ref="treeRef"
			style="max-width: 600px"
			:data="roleList"
			:props="defaultProps"
			show-checkbox
			node-key="id"
			default-expand-all
			:default-checked-keys="selectArr"
		/>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="submit">确认</el-button>
				<el-button type="primary" @click="handleClose">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { ref, reactive, onMounted, nextTick } from 'vue';
import { reqAllMenuList, reqSetPermisstion } from '@/api/acl/role';
let roleArr = ref([]);
let props = defineProps({
	roleAuthDiog: {
		type: Boolean,
		default: false,
	},
	roleId: {
		type: Number,
		default: '',
	},
});

let treeRef = ref();
const defaultProps = {
	children: 'children',
	label: 'name',
};
let selectArr = ref<number[]>([]);
let emit = defineEmits(['closeRoleDiog', 'getList']);
let roleList = ref([]);
onMounted(() => {
	getListAuthority(props.roleId);
});
let getListAuthority = async (id: number) => {
	let res = await reqAllMenuList(id);
	if (res.code == 200) {
		roleList.value = res.data;
		nextTick(() => {
			selectArr.value = filterSelectArr(roleList.value, []);
			console.log(selectArr.value);
		});
	}
};
const filterSelectArr = (allData: any, initArr: any) => {
	allData.forEach((item: any) => {
		if (item.select && item.level == 4) {
			initArr.push(item.id);
		}
		if (item.children && item.children.length > 0) {
			filterSelectArr(item.children, initArr);
		}
	});

	return initArr;
};
let submit = async () => {
	let roids = treeRef.value!.getCheckedKeys(false);
	let res = await reqSetPermisstion(props.roleId, roids);
	if (res.code == 200) {
		ElMessage.success('设置权限成功');
		emit('getList');
		emit('closeRoleDiog');
	} else {
		ElMessage.error('设置权限失败');
	}
};
let handleClose = () => {
	emit('closeRoleDiog');
};
</script>

<style lang="scss" scoped></style>
