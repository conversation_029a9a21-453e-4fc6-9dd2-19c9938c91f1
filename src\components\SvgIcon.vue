<template>
	<div>
		<svg :style="{ width, height }">
			<use :xlink:href="prefix + name" :fill="color"></use>
		</svg>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	width: {
		type: String,
		default: '16px',
	},
	height: {
		type: String,
		default: '16px',
	},
	prefix: {
		type: String,
		default: '#icon-',
	},
	name: String,
	//svg图标的颜色
	color: {
		type: String,
		default: '',
	},
});
</script>
