<template>
	<el-dialog
		:modelValue="dialogVisible"
		:title="props.itemObj.id ? '修改角色' : '添加角色'"
		width="500"
		:before-close="handleClose"
	>
		<el-form
			ref="formRef"
			:model="formData"
			label-width="auto"
			class="demo-ruleForm"
			:rules="rules"
		>
			<el-form-item label="角色名称" prop="roleName">
				<el-input v-model="formData.roleName" type="text" autocomplete="off" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="submit">确认</el-button>
				<el-button type="primary" @click="handleClose">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { reqAddOrUpdateRole } from '@/api/acl/role';
import { ElMessage } from 'element-plus';
let props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false,
	},
	itemObj: {
		type: Object,
		default: () => ({}),
	},
});
let formRef = ref();
let emit = defineEmits(['closeDiog', 'getList']);

onMounted(() => {
	console.log(props.itemObj);
	if (props.itemObj) {
		Object.assign(formData, props.itemObj);
	} else {
		Object.assign(formData, { roleName: '' });
	}
});

let formData = reactive({
	roleName: '',
});
let rules = reactive({
	roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
});
let handleClose = () => {
	Object.assign(formData, { roleName: '' });
	emit('closeDiog');
};
let submit = async () => {
	await formRef.value.validate();
	if (props.itemObj.id) {
		let res = await reqAddOrUpdateRole(formData);
		if (res.code == 200) {
			ElMessage.success(res.message);
			emit('closeDiog');
			emit('getList');
		} else {
      ElMessage.error(res.message);
		}
	} else {
		let res = await reqAddOrUpdateRole(formData);
		if (res.code == 200) {
			ElMessage.success(res.message);
			emit('closeDiog');
			emit('getList');
		} else {
			ElMessage.error(res.message);
		}
	}
};
</script>

<style lang="scss" scoped></style>
