<template>
  <!-- defineProps传过来是单向数据流，v-model不能使用 -->
	<el-dialog
		v-model="dialogVisible"
		title="Tips"
		width="500"
		:before-close="handleClose"
	>
		<span>This is a message</span>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="cancel">Cancel</el-button>
				<el-button type="primary" @click="handleClose">Confirm</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
let props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false,
	},
});
const emits = defineEmits(['update:modelValue', 'closeDiog']);

const dialogVisible = computed({
	// 重新定义
	get: () => props.dialogVisible,
	set: (value) => emits('update:modelValue', value),
});
let cancel = () => {};
let handleClose = () => {
	emits('closeDiog');
};
</script>
