<template>
	<el-dialog
		draggable
		:modelValue="dialogVisible"
		title="添加用户"
		width="500"
		:before-close="handleClose"
	>
		<template #header>
			<div class="my-header">
				<div>{{ updateId ? '编辑用户' : '添加用户' }}</div>
			</div>
		</template>
		<el-form
			ref="ruleFormRef"
			style="max-width: 600px"
			:model="ruleForm"
			:rules="rules"
			label-width="auto"
			class="demo-ruleForm"
			status-icon
		>
			<el-form-item label="用户名" prop="username">
				<el-input v-model="ruleForm.username" />
			</el-form-item>
			<el-form-item label="昵称" prop="name">
				<el-input v-model="ruleForm.name" />
			</el-form-item>
			<el-form-item label="密码" prop="password">
				<el-input v-model="ruleForm.password" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="cancel" size="mini">确认</el-button>
				<el-button type="primary" @click="handleClose" size="mini">
					取消
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, onMounted, nextTick } from 'vue';
import { reqAddOrUpdateUser } from '@/api/acl/user';
import { ElMessage } from 'element-plus';
let props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false,
	},
	updateId: {
		type: Number,
	},
	updateUserForm: {
		type: Object,
	},
});
const emits = defineEmits(['closeDiog', 'getList']);
let ruleForm = reactive({
	username: '',
	name: '',
	password: '',
});
let rules = reactive({
	username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
	name: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
	password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});
let ruleFormRef = ref();
onMounted(() => {
	if (props.updateId) {
		Object.assign(ruleForm, props.updateUserForm);
	} else {
		Object.assign(ruleForm, {
			username: '',
			name: '',
			password: '',
		});
	}
});
let cancel = async () => {
	await ruleFormRef.value.validate();
	if (props.updateId) {
		Object.assign(ruleForm, { id: props.updateId });
		let result = await reqAddOrUpdateUser(ruleForm);
		if (result.code == 200) {
			ElMessage.success(result.message);
			emits('closeDiog');
			emits('getList');
		} else {
			ElMessage.error(result.message);
		}
	} else {
		let result = await reqAddOrUpdateUser(ruleForm);
		if (result.code == 200) {
			ElMessage.success(result.message);
			emits('closeDiog');
			emits('getList');
		} else {
			ElMessage.error(result.message);
		}
	}
};
let handleClose = () => {
	Object.assign(ruleForm, {
		username: '',
		name: '',
		password: '',
	});
	ruleFormRef.value.resetFields();
	emits('closeDiog');
};
</script>
