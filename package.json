{"name": "hello_vue3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "element-plus": "^2.9.1", "lodash": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^5.0.3", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^2.1.7", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/node": "^18.17.17", "@types/nprogress": "^0.2.3", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.4.0", "fast-glob": "^3.3.2", "mockjs": "^1.1.0", "npm-run-all2": "^6.0.6", "sass": "^1.83.0", "sass-loader": "^16.0.4", "typescript": "~5.2.0", "unocss": "^66.3.3", "vite": "^4.4.9", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.8.11"}}