// 常量的静态路由
export const constantRoutes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      icon: 'House',
      hidden: true
    }
  },
  {
    path: '/',
    name: 'loyout',
    component: () => import('@/layout/index.vue'),
    redirect: '/home', // 默认跳转
    meta: {
      title: '首页',
      icon: '',
      // hidden: false
    },
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'House',
          // hidden: false,
          noCache : true,
          affix : true
        }
      }
    ]
  },
  {
    path: '/redirect',
    name: 'Redirect',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: 'loyout',
      hidden: true
    },
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        // component: () => import('@/views/redirect/index.vue'),
        component: () => import('@/views/redirect/index.vue'),
        meta: {
          title: 'Redirect',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404/index.vue'),
    meta: {
      title: '404',
      icon: 'House',
      hidden: true
    }
  },
]
// 异步路由
export const asyncRoutes = [
  {
    path: '/acl',
    name: 'Acl',
    component: () => import('@/layout/index.vue'),
    redirect: '/acl/user',
    meta: {
      title: '权限管理',
      icon: 'House',
      hidden: false
    },
    children: [
      {
        path: '/acl/user',
        name: 'User',
        component: () => import('@/views/acl/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'House',
          hidden: false
        },
      },
      {
        path: '/acl/role',
        name: 'Role',
        component: () => import('@/views/acl/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'House',
          hidden: false
        }
      },
      {
        path: '/acl/permission',
        name: 'Permission',
        component: () => import('@/views/acl/permission/index.vue'),
        meta: {
          title: '菜单管理',
          icon: 'House',
          hidden: false
        },
      }
    ]
  },
  {
    path: '/product',
    name: 'Product',
    component: () => import('@/layout/index.vue'),
    redirect: '/product/attr',
    meta: {
      title: '商品管理',
      icon: 'House',
      hidden: false
    },
    children: [
      {
        path: '/product/attr',
        name: 'Attr',
        component: () => import('@/views/product/attr/index.vue'),
        meta: {
          title: '商品属性',
          icon: 'House',
          hidden: false
        },
      },
      {
        path: '/product/sku',
        name: 'Sku',
        component: () => import('@/views/product/sku/index.vue'),
        meta: {
          title: '商品sku',
          icon: 'House',
          hidden: false
        }
      },
      {
        path: '/product/spu',
        name: 'Spu',
        component: () => import('@/views/product/spu/index.vue'),
        meta: {
          title: '商品spu',
          icon: 'House',
          hidden: false
        },
      },
      {
        path: '/product/trademark',
        name: 'Trademark',
        component: () => import('@/views/product/trademark/index.vue'),
        meta: {
          title: '商品trademark',
          icon: 'House',
          hidden: false
        },
      }
    ]
  },
]

// 任意路由
export const anyRouter = [{
  path: '/:pathMatch(.*)*',
  name: 'Any',
  component: () => import('@/views/404/index.vue'),
  meta: {
    title: '任意页面',
    icon: 'House',
    hidden: true
  }
},

]