<template>
  <div>
    <el-card>
      <el-table
        :data="permisstionList"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
      >
        <el-table-column prop="name" label="名称" show-overflow-tooltip />
        <el-table-column prop="code" label="权限值" show-overflow-tooltip />
        <el-table-column
          prop="updateTime"
          label="修改时间"
          show-overflow-tooltip
        />
        <el-table-column label="操作" fixed="right" width="220" align="center">
          <template #="{ row, $index }">
            <el-button size="small" @click="add"> 添加 </el-button>
            <el-button size="small" @click="editPage(row)"> 编辑 </el-button>
            <el-button size="small" type="danger" @click="del(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { reqAllPermisstion } from "@/api/acl/menu";

let permisstionList = ref([]);
onMounted(() => {
  reqAllPermisstionList();
});
let reqAllPermisstionList = async () => {
  let res: any = await reqAllPermisstion;
  if (res.code == 200) {
    permisstionList.value = res.data;
  }
};
let add = () => {};
let editPage = (row: any) => {};
let del = (row: any) => {};
</script>