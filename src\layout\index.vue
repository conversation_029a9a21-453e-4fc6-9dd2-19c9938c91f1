<template>
  <div class="layout_container">
    <!-- 左侧菜单 -->
    <div
      class="layout_slider"
      :class="{ fold: settingStore.fold ? true : false }"
    >
      <logo></logo>
      <!-- 展示菜单 -->
      <el-scrollbar class="scrollbar">
        <el-menu
          active-text-color="#ffd04b"
          background-color="#001529"
          text-color="#fff"
          :default-active="$route.path"
          :collapse="settingStore.fold"
        >
          <menus :menuList="userStore.menuRouters" />
        </el-menu>
      </el-scrollbar>
    </div>
    <!-- 顶部导航 -->
    <div
      class="layout_tabbar"
      :class="{ fold: settingStore.fold ? true : false }"
    >
      <tabbar></tabbar>
    </div>

    <!-- 主体内容 -->
    <div
      class="layout_main"
      :class="{ fold: settingStore.fold ? true : false }"
    >
      <tagsView style="margin-top: 1px" />
      <mainPage></mainPage>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import useUserStore from "@/store/model/user";
import useSettingStore from "@/store/model/setting";
import logo from "./logo/index.vue";
import menus from "./menu/index.vue";
import mainPage from "./main/index.vue";
import tabbar from "./tabbar/index.vue";
import tagsView from "./tagsView/index.vue";
let $route = useRoute();
console.log($route.path, "$route.path");
let settingStore = useSettingStore();
let userStore = useUserStore();
</script>
<style lang="scss" scoped>
.layout_container {
  width: 100%;
  height: 100vh;

  .layout_slider {
    width: $base-menu-width;
    height: 100vh;
    background-color: $base-menu-background;
    transition: all 0.3s;

    &.fold {
      width: $base-menu-min-width;
    }

    .scrollbar {
      width: 100%;
      height: calc(100vh - $base-menu-logo-height);

      .el-menu {
        border: none;
      }
    }
  }

  .layout_tabbar {
    position: fixed;
    left: $base-menu-width;
    top: 0;
    width: calc(100% - $base-menu-width);
    height: $base-tabbar-height;
    transition: all 0.3s;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    &.fold {
      left: $base-menu-min-width;
      width: calc(100vw - $base-menu-min-width);
    }
  }

  .layout_main {
    position: absolute;
    left: $base-menu-width;
    top: $base-tabbar-height;
    width: calc(100% - $base-menu-width);
    height: calc(100vh - $base-tabbar-height);
    overflow: auto;
    transition: all 0.3s;

    &.fold {
      left: $base-menu-min-width;
      width: calc(100vw - $base-menu-min-width);
    }
  }
}
</style>
